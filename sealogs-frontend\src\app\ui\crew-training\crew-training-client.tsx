'use client'

import React, { useRef, useState, useEffect, useMemo, useCallback } from 'react'
import CrewTrainingList from './list'

import { CrewTrainingFilterActions } from '@/components/filter/components/training-actions'
import { SealogsTrainingIcon } from '@/app/lib/icons/SealogsTrainingIcon'
import { ListHeader } from '@/components/ui/list-header'
import { useQueryState, parseAsBoolean } from 'nuqs'
import { UnifiedTrainingTable } from './unified-training-table'
import { useLazyQuery } from '@apollo/client'
import {
    TRAINING_SESSIONS,
    READ_TRAINING_SESSION_DUES,
} from '@/app/lib/graphQL/query'
import { useVesselIconData } from '@/app/lib/vessel-icon-helper'
import { mergeAndSortCrewTrainingData } from '@/app/ui/crew-training/utils/crew-training-utils'
import { useUnifiedTrainingFilters } from './hooks/useUnifiedTrainingFilters'
import { getPermissions, hasPermission } from '@/app/helpers/userHelper'

type FilterHandle = {
    apply: (p: { type: string; data: any }) => void
    overdue: boolean // read-only snapshot
    setOverdue: (v: boolean) => void
}

const CrewTrainingClient = () => {
    const applyFilterRef = useRef<FilterHandle>(null)

    /** ⬅️ 1) reactive state that drives the heading */
    const [isOverdueEnabled, setIsOverdueEnabled] = useQueryState(
        'overdue',
        parseAsBoolean.withDefault(false),
    )

    // Training data state
    const [isLoading, setIsLoading] = useState(false)
    const [trainingSessionDues, setTrainingSessionDues] = useState<any[]>([])
    const [completedTrainingList, setCompletedTrainingList] = useState<any[]>([])
    const [includeCompleted, setIncludeCompleted] = useState(true)

    // GraphQL queries
    const [loadTrainingSessionDues] = useLazyQuery(READ_TRAINING_SESSION_DUES, {
        onCompleted: (data) => {
            setTrainingSessionDues(data.readTrainingSessionDues?.nodes || [])
        },
        onError: (error) => {
            console.error('Error loading training session dues:', error)
        },
    })

    const [loadTrainingList] = useLazyQuery(TRAINING_SESSIONS, {
        onCompleted: (data) => {
            setCompletedTrainingList(data.readTrainingSessions?.nodes || [])
        },
        onError: (error) => {
            console.error('Error loading training sessions:', error)
        },
    })

    // Vessel icon helper
    const { getVesselWithIcon } = useVesselIconData()

    // Memoize the unified data calculation
    const unifiedData = useMemo(() => {
        return mergeAndSortCrewTrainingData({
            trainingSessionDues,
            completedTrainingList,
            getVesselWithIcon,
            includeCompleted,
        })
    }, [
        trainingSessionDues,
        completedTrainingList,
        getVesselWithIcon,
        includeCompleted,
    ])

    // Use unified training filters hook with client-side filtering
    const { handleFilterChange, filteredData } = useUnifiedTrainingFilters({
        initialFilter: {},
        unifiedData,
    })

    // Load data on component mount
    useEffect(() => {
        const loadData = async () => {
            setIsLoading(true)
            try {
                await Promise.all([
                    loadTrainingSessionDues({}),
                    loadTrainingList(0, {}),
                ])
            } catch (error) {
                console.error('Error loading training data:', error)
            } finally {
                setIsLoading(false)
            }
        }

        loadData()
    }, [loadTrainingSessionDues, loadTrainingList])

    /** ⬅️ 2) keep both the list and the heading in sync */
    const handleDropdownChange = (type: string, data: any) => {
        if (type === 'overdue') setIsOverdueEnabled(!!data)
        applyFilterRef.current?.apply({ type, data })
    }

    // Handle filter changes from the FilterTable toolbar
    const handleFilterTableChange = useCallback((filterData: { type: string; data: any }) => {
        handleFilterChange(filterData)
    }, [handleFilterChange])

    return (
        <>
            <ListHeader
                icon={<SealogsTrainingIcon className="size-12" />}
                title={'Crew Trainings'}
                actions={
                    <CrewTrainingFilterActions
                        onChange={(data: any) => {
                            handleDropdownChange('overdue', data)
                        }}
                        overdueList={isOverdueEnabled}
                    />
                }
            />
            <div className="mt-16">
                <UnifiedTrainingTable
                    unifiedData={filteredData}
                    getVesselWithIcon={getVesselWithIcon}
                    includeCompleted={includeCompleted}
                    isVesselView={false}
                    showToolbar={true}
                    isLoading={isLoading}
                    pageSize={20}
                    onChange={handleFilterTableChange}
                />
            </div>
        </>
    )
}

export default CrewTrainingClient
