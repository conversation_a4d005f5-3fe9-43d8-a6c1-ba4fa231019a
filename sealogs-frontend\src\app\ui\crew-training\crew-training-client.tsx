'use client'

import React, { useRef, useState, useEffect, useMemo, useCallback } from 'react'
import CrewTrainingList from './list'

import { CrewTrainingFilterActions } from '@/components/filter/components/training-actions'
import { SealogsTrainingIcon } from '@/app/lib/icons/SealogsTrainingIcon'
import { ListHeader } from '@/components/ui/list-header'
import { useQueryState, parseAsBoolean } from 'nuqs'
import { UnifiedTrainingTable } from './unified-training-table'
import { useLazyQuery } from '@apollo/client'
import {
    TRAINING_SESSIONS,
    READ_TRAINING_SESSION_DUES,
} from '@/app/lib/graphQL/query'
import { useVesselIconData } from '@/app/lib/vessel-icon-helper'
import { mergeAndSortCrewTrainingData } from '@/app/ui/crew-training/utils/crew-training-utils'
import { useUnifiedTrainingFilters } from './hooks/useUnifiedTrainingFilters'
import { getPermissions, hasPermission } from '@/app/helpers/userHelper'
import Loading from '@/app/loading'
import { GetTrainingSessionStatus } from '@/app/lib/actions'

type FilterHandle = {
    apply: (p: { type: string; data: any }) => void
    overdue: boolean // read-only snapshot
    setOverdue: (v: boolean) => void
}

const CrewTrainingClient = () => {
    const applyFilterRef = useRef<FilterHandle>(null)

    /** ⬅️ 1) reactive state that drives the heading */
    const [isOverdueEnabled, setIsOverdueEnabled] = useQueryState(
        'overdue',
        parseAsBoolean.withDefault(false),
    )

    // Training data state
    const [isLoading, setIsLoading] = useState(false)
    const [trainingSessionDues, setTrainingSessionDues] = useState<any[]>([])
    const [completedTrainingList, setCompletedTrainingList] = useState<any[]>(
        [],
    )
    const [includeCompleted, setIncludeCompleted] = useState(true)
    const [permissions, setPermissions] = useState<any>(false)

    // GraphQL queries
    const [loadTrainingSessionDues] = useLazyQuery(READ_TRAINING_SESSION_DUES, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response) => {
            const data = response.readTrainingSessionDues.nodes
            if (data) {
                // Filter out crew members who are no longer assigned to the vessel (same as list.tsx)
                const filteredData = data.filter((item: any) =>
                    item.vessel.seaLogsMembers.nodes.some((m: any) => {
                        return m.id === item.memberID
                    }),
                )
                const dueWithStatus = filteredData.map((due: any) => {
                    return { ...due, status: GetTrainingSessionStatus(due) }
                })

                // Group dues by vessel, training type, and due date (same logic as list.tsx)
                const groupedDues = dueWithStatus.reduce(
                    (acc: any, due: any) => {
                        const key = `${due.vesselID}-${due.trainingTypeID}-${due.dueDate}`
                        if (!acc[key]) {
                            acc[key] = {
                                id: due.id,
                                vesselID: due.vesselID,
                                vessel: due.vessel,
                                trainingTypeID: due.trainingTypeID,
                                trainingType: due.trainingType,
                                dueDate: due.dueDate,
                                status: due.status,
                                trainingLocationType:
                                    due.trainingSession?.trainingLocationType,
                                members: [],
                            }
                        }
                        acc[key].members.push(due.member)
                        return acc
                    },
                    {},
                )

                const mergedDues = Object.values(groupedDues).map(
                    (group: any) => {
                        const mergedMembers = group.members.reduce(
                            (acc: any, member: any) => {
                                const existingMember = acc.find(
                                    (m: any) => m.id === member.id,
                                )
                                if (existingMember) {
                                    existingMember.firstName = member.firstName
                                    existingMember.surname = member.surname
                                } else {
                                    acc.push(member)
                                }
                                return acc
                            },
                            [],
                        )
                        return {
                            id: group.id,
                            vesselID: group.vesselID,
                            vessel: group.vessel,
                            trainingTypeID: group.trainingTypeID,
                            trainingType: group.trainingType,
                            status: group.status,
                            dueDate: group.dueDate,
                            trainingLocationType: group.trainingLocationType,
                            members: mergedMembers,
                        }
                    },
                )
                setTrainingSessionDues(mergedDues)
            }
        },
        onError: (error) => {
            console.error('Error loading training session dues:', error)
        },
    })

    const [loadTrainingList] = useLazyQuery(TRAINING_SESSIONS, {
        onCompleted: (data) => {
            setCompletedTrainingList(data.readTrainingSessions?.nodes || [])
        },
        onError: (error) => {
            console.error('Error loading training sessions:', error)
        },
    })

    // Vessel icon helper
    const { getVesselWithIcon } = useVesselIconData()

    // Memoize the unified data calculation
    const unifiedData = useMemo(() => {
        return mergeAndSortCrewTrainingData({
            trainingSessionDues,
            completedTrainingList,
            getVesselWithIcon,
            includeCompleted,
        })
    }, [
        trainingSessionDues,
        completedTrainingList,
        getVesselWithIcon,
        includeCompleted,
    ])

    // Use unified training filters hook with client-side filtering
    const { handleFilterChange, filteredData } = useUnifiedTrainingFilters({
        initialFilter: {},
        unifiedData,
    })

    // Initialize permissions
    useEffect(() => {
        setPermissions(getPermissions)
    }, [])

    // Load data on component mount
    useEffect(() => {
        const loadData = async () => {
            setIsLoading(true)
            try {
                await Promise.all([
                    loadTrainingSessionDues({ variables: { filter: {} } }),
                    loadTrainingList({
                        variables: { filter: {}, offset: 0, limit: 100 },
                    }),
                ])
            } catch (error) {
                console.error('Error loading training data:', error)
            } finally {
                setIsLoading(false)
            }
        }

        loadData()
    }, [loadTrainingSessionDues, loadTrainingList])

    /** ⬅️ 2) keep both the list and the heading in sync */
    const handleDropdownChange = (type: string, data: any) => {
        if (type === 'overdue') setIsOverdueEnabled(!!data)
        applyFilterRef.current?.apply({ type, data })
    }

    // Handle filter changes from the FilterTable toolbar
    const handleFilterTableChange = useCallback(
        (filterData: { type: string; data: any }) => {
            handleFilterChange(filterData)
        },
        [handleFilterChange],
    )

    // Permission guards - same as in list.tsx
    if (
        !permissions ||
        (!hasPermission('EDIT_TRAINING', permissions) &&
            !hasPermission('VIEW_TRAINING', permissions) &&
            !hasPermission('RECORD_TRAINING', permissions) &&
            !hasPermission('VIEW_MEMBER_TRAINING', permissions))
    ) {
        return !permissions ? (
            <Loading />
        ) : (
            <Loading errorMessage="Oops You do not have the permission to view this section." />
        )
    }

    return (
        <>
            <ListHeader
                icon={<SealogsTrainingIcon className="size-12" />}
                title={'Crew Trainings'}
                actions={
                    <CrewTrainingFilterActions
                        onChange={(data: any) => {
                            handleDropdownChange('overdue', data)
                        }}
                        overdueList={isOverdueEnabled}
                    />
                }
            />
            <div className="mt-16">
                <UnifiedTrainingTable
                    unifiedData={filteredData}
                    getVesselWithIcon={getVesselWithIcon}
                    includeCompleted={includeCompleted}
                    isVesselView={false}
                    showToolbar={true}
                    isLoading={isLoading}
                    pageSize={20}
                    onChange={handleFilterTableChange}
                />
            </div>
        </>
    )
}

export default CrewTrainingClient
